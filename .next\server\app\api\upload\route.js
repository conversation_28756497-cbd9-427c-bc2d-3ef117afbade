"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload/route";
exports.ids = ["app/api/upload/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CSpaces%5CDaisyLogsCursor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CSpaces%5CDaisyLogsCursor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CSpaces%5CDaisyLogsCursor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CSpaces%5CDaisyLogsCursor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Spaces_DaisyLogsCursor_src_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload/route.ts */ \"(rsc)/./src/app/api/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload/route\",\n        pathname: \"/api/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload/route\"\n    },\n    resolvedPagePath: \"C:\\\\Spaces\\\\DaisyLogsCursor\\\\src\\\\app\\\\api\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Spaces_DaisyLogsCursor_src_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/upload/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CSpaces%5CDaisyLogsCursor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CSpaces%5CDaisyLogsCursor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/upload/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/upload/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_urlExtractor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/urlExtractor */ \"(rsc)/./src/utils/urlExtractor.ts\");\n/* harmony import */ var _utils_urlCategorizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/urlCategorizer */ \"(rsc)/./src/utils/urlCategorizer.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const file = formData.get(\"file\");\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"No file provided\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate file type\n        const allowedTypes = [\n            \"text/plain\",\n            \"text/csv\",\n            \"application/json\"\n        ];\n        if (!allowedTypes.includes(file.type) && !file.name.endsWith(\".txt\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid file type. Please upload a text file (.txt, .csv, or .json)\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate file size (max 10MB)\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        if (file.size > maxSize) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"File too large. Maximum size is 10MB\"\n            }, {\n                status: 400\n            });\n        }\n        // Read file content\n        const content = await file.text();\n        if (!content.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"File is empty\"\n            }, {\n                status: 400\n            });\n        }\n        // Extract URLs from content\n        const extractedUrls = (0,_utils_urlExtractor__WEBPACK_IMPORTED_MODULE_1__.extractUrls)(content);\n        if (extractedUrls.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"No URLs found in the file\",\n                data: {\n                    totalUrls: 0,\n                    categorizedUrls: [],\n                    groupedUrls: {},\n                    categoryStats: {},\n                    fileName: file.name,\n                    fileSize: file.size\n                }\n            });\n        }\n        // Categorize URLs\n        const categorizedUrls = (0,_utils_urlCategorizer__WEBPACK_IMPORTED_MODULE_2__.categorizeUrls)(extractedUrls);\n        // Group URLs by category\n        const groupedUrls = (0,_utils_urlCategorizer__WEBPACK_IMPORTED_MODULE_2__.groupUrlsByCategory)(categorizedUrls);\n        // Get category statistics\n        const categoryStats = (0,_utils_urlCategorizer__WEBPACK_IMPORTED_MODULE_2__.getCategoryStats)(categorizedUrls);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Found ${extractedUrls.length} URLs in ${Object.keys(categoryStats).length} categories`,\n            data: {\n                totalUrls: extractedUrls.length,\n                categorizedUrls,\n                groupedUrls,\n                categoryStats,\n                fileName: file.name,\n                fileSize: file.size\n            }\n        });\n    } catch (error) {\n        console.error(\"Error processing file:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error while processing file\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle GET requests (not allowed)\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Method not allowed. Use POST to upload files.\"\n    }, {\n        status: 405\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/urlCategorizer.ts":
/*!*************************************!*\
  !*** ./src/utils/urlCategorizer.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   URL_CATEGORIES: () => (/* binding */ URL_CATEGORIES),\n/* harmony export */   categorizeUrl: () => (/* binding */ categorizeUrl),\n/* harmony export */   categorizeUrls: () => (/* binding */ categorizeUrls),\n/* harmony export */   getCategoryStats: () => (/* binding */ getCategoryStats),\n/* harmony export */   groupUrlsByCategory: () => (/* binding */ groupUrlsByCategory)\n/* harmony export */ });\n// Define URL categories\nconst URL_CATEGORIES = {\n    \"Social Media\": {\n        name: \"Social Media\",\n        color: \"#3B82F6\",\n        description: \"Social networking and media sharing platforms\"\n    },\n    \"News & Media\": {\n        name: \"News & Media\",\n        color: \"#EF4444\",\n        description: \"News websites and media outlets\"\n    },\n    \"E-commerce\": {\n        name: \"E-commerce\",\n        color: \"#10B981\",\n        description: \"Online shopping and marketplace websites\"\n    },\n    \"Technology\": {\n        name: \"Technology\",\n        color: \"#8B5CF6\",\n        description: \"Technology companies and tech news\"\n    },\n    \"Entertainment\": {\n        name: \"Entertainment\",\n        color: \"#F59E0B\",\n        description: \"Entertainment, gaming, and streaming platforms\"\n    },\n    \"Education\": {\n        name: \"Education\",\n        color: \"#06B6D4\",\n        description: \"Educational institutions and learning platforms\"\n    },\n    \"Business\": {\n        name: \"Business\",\n        color: \"#84CC16\",\n        description: \"Business and professional services\"\n    },\n    \"Government\": {\n        name: \"Government\",\n        color: \"#6B7280\",\n        description: \"Government and official websites\"\n    },\n    \"Other\": {\n        name: \"Other\",\n        color: \"#9CA3AF\",\n        description: \"Uncategorized websites\"\n    }\n};\n// Domain to category mapping\nconst DOMAIN_CATEGORIES = {\n    // Social Media\n    \"facebook.com\": \"Social Media\",\n    \"instagram.com\": \"Social Media\",\n    \"tiktok.com\": \"Social Media\",\n    \"lemon8-app.com\": \"Social Media\",\n    \"twitter.com\": \"Social Media\",\n    \"x.com\": \"Social Media\",\n    \"linkedin.com\": \"Social Media\",\n    \"snapchat.com\": \"Social Media\",\n    \"pinterest.com\": \"Social Media\",\n    \"reddit.com\": \"Social Media\",\n    \"discord.com\": \"Social Media\",\n    \"telegram.org\": \"Social Media\",\n    \"whatsapp.com\": \"Social Media\",\n    \"youtube.com\": \"Social Media\",\n    \"vimeo.com\": \"Social Media\",\n    // News & Media\n    \"cnn.com\": \"News & Media\",\n    \"bbc.com\": \"News & Media\",\n    \"nytimes.com\": \"News & Media\",\n    \"washingtonpost.com\": \"News & Media\",\n    \"reuters.com\": \"News & Media\",\n    \"ap.org\": \"News & Media\",\n    \"npr.org\": \"News & Media\",\n    \"theguardian.com\": \"News & Media\",\n    \"wsj.com\": \"News & Media\",\n    \"bloomberg.com\": \"News & Media\",\n    // E-commerce\n    \"amazon.com\": \"E-commerce\",\n    \"ebay.com\": \"E-commerce\",\n    \"etsy.com\": \"E-commerce\",\n    \"shopify.com\": \"E-commerce\",\n    \"walmart.com\": \"E-commerce\",\n    \"target.com\": \"E-commerce\",\n    \"alibaba.com\": \"E-commerce\",\n    \"aliexpress.com\": \"E-commerce\",\n    // Technology\n    \"google.com\": \"Technology\",\n    \"microsoft.com\": \"Technology\",\n    \"apple.com\": \"Technology\",\n    \"github.com\": \"Technology\",\n    \"stackoverflow.com\": \"Technology\",\n    \"techcrunch.com\": \"Technology\",\n    \"wired.com\": \"Technology\",\n    \"verge.com\": \"Technology\",\n    // Entertainment\n    \"netflix.com\": \"Entertainment\",\n    \"hulu.com\": \"Entertainment\",\n    \"disney.com\": \"Entertainment\",\n    \"spotify.com\": \"Entertainment\",\n    \"twitch.tv\": \"Entertainment\",\n    \"steam.com\": \"Entertainment\",\n    \"imdb.com\": \"Entertainment\",\n    // Education\n    \"coursera.org\": \"Education\",\n    \"edx.org\": \"Education\",\n    \"khanacademy.org\": \"Education\",\n    \"udemy.com\": \"Education\",\n    \"mit.edu\": \"Education\",\n    \"harvard.edu\": \"Education\",\n    \"stanford.edu\": \"Education\",\n    // Business\n    \"salesforce.com\": \"Business\",\n    \"hubspot.com\": \"Business\",\n    \"slack.com\": \"Business\",\n    \"zoom.us\": \"Business\",\n    \"dropbox.com\": \"Business\",\n    // Government\n    \"gov\": \"Government\",\n    \"whitehouse.gov\": \"Government\",\n    \"irs.gov\": \"Government\",\n    \"cdc.gov\": \"Government\"\n};\n/**\n * Categorizes a URL based on its domain\n */ function categorizeUrl(extractedUrl) {\n    const domain = extractedUrl.domain.toLowerCase();\n    // Check exact domain match\n    let categoryName = DOMAIN_CATEGORIES[domain];\n    // Check for subdomain matches (e.g., subdomain.facebook.com)\n    if (!categoryName) {\n        for (const [domainPattern, category] of Object.entries(DOMAIN_CATEGORIES)){\n            if (domain.endsWith(domainPattern)) {\n                categoryName = category;\n                break;\n            }\n        }\n    }\n    // Check for TLD-based categorization (e.g., .gov domains)\n    if (!categoryName) {\n        if (domain.endsWith(\".gov\")) {\n            categoryName = \"Government\";\n        } else if (domain.endsWith(\".edu\")) {\n            categoryName = \"Education\";\n        }\n    }\n    // Default to 'Other' if no category found\n    if (!categoryName) {\n        categoryName = \"Other\";\n    }\n    return {\n        ...extractedUrl,\n        category: URL_CATEGORIES[categoryName]\n    };\n}\n/**\n * Categorizes multiple URLs\n */ function categorizeUrls(extractedUrls) {\n    return extractedUrls.map(categorizeUrl);\n}\n/**\n * Groups categorized URLs by category\n */ function groupUrlsByCategory(categorizedUrls) {\n    return categorizedUrls.reduce((groups, url)=>{\n        const categoryName = url.category.name;\n        if (!groups[categoryName]) {\n            groups[categoryName] = [];\n        }\n        groups[categoryName].push(url);\n        return groups;\n    }, {});\n}\n/**\n * Gets category statistics\n */ function getCategoryStats(categorizedUrls) {\n    return categorizedUrls.reduce((stats, url)=>{\n        const categoryName = url.category.name;\n        stats[categoryName] = (stats[categoryName] || 0) + 1;\n        return stats;\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvdXJsQ2F0ZWdvcml6ZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFZQSx3QkFBd0I7QUFDakIsTUFBTUEsaUJBQThDO0lBQ3pELGdCQUFnQjtRQUNkQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0EsZ0JBQWdCO1FBQ2RGLE1BQU07UUFDTkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQSxjQUFjO1FBQ1pGLE1BQU07UUFDTkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQSxjQUFjO1FBQ1pGLE1BQU07UUFDTkMsT0FBTztRQUNQQyxhQUFhO0lBQ2Y7SUFDQSxpQkFBaUI7UUFDZkYsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBLGFBQWE7UUFDWEYsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBLFlBQVk7UUFDVkYsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBLGNBQWM7UUFDWkYsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBLFNBQVM7UUFDUEYsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtBQUNGLEVBQUU7QUFFRiw2QkFBNkI7QUFDN0IsTUFBTUMsb0JBQTRDO0lBQ2hELGVBQWU7SUFDZixnQkFBZ0I7SUFDaEIsaUJBQWlCO0lBQ2pCLGNBQWM7SUFDZCxrQkFBa0I7SUFDbEIsZUFBZTtJQUNmLFNBQVM7SUFDVCxnQkFBZ0I7SUFDaEIsZ0JBQWdCO0lBQ2hCLGlCQUFpQjtJQUNqQixjQUFjO0lBQ2QsZUFBZTtJQUNmLGdCQUFnQjtJQUNoQixnQkFBZ0I7SUFDaEIsZUFBZTtJQUNmLGFBQWE7SUFFYixlQUFlO0lBQ2YsV0FBVztJQUNYLFdBQVc7SUFDWCxlQUFlO0lBQ2Ysc0JBQXNCO0lBQ3RCLGVBQWU7SUFDZixVQUFVO0lBQ1YsV0FBVztJQUNYLG1CQUFtQjtJQUNuQixXQUFXO0lBQ1gsaUJBQWlCO0lBRWpCLGFBQWE7SUFDYixjQUFjO0lBQ2QsWUFBWTtJQUNaLFlBQVk7SUFDWixlQUFlO0lBQ2YsZUFBZTtJQUNmLGNBQWM7SUFDZCxlQUFlO0lBQ2Ysa0JBQWtCO0lBRWxCLGFBQWE7SUFDYixjQUFjO0lBQ2QsaUJBQWlCO0lBQ2pCLGFBQWE7SUFDYixjQUFjO0lBQ2QscUJBQXFCO0lBQ3JCLGtCQUFrQjtJQUNsQixhQUFhO0lBQ2IsYUFBYTtJQUViLGdCQUFnQjtJQUNoQixlQUFlO0lBQ2YsWUFBWTtJQUNaLGNBQWM7SUFDZCxlQUFlO0lBQ2YsYUFBYTtJQUNiLGFBQWE7SUFDYixZQUFZO0lBRVosWUFBWTtJQUNaLGdCQUFnQjtJQUNoQixXQUFXO0lBQ1gsbUJBQW1CO0lBQ25CLGFBQWE7SUFDYixXQUFXO0lBQ1gsZUFBZTtJQUNmLGdCQUFnQjtJQUVoQixXQUFXO0lBQ1gsa0JBQWtCO0lBQ2xCLGVBQWU7SUFDZixhQUFhO0lBQ2IsV0FBVztJQUNYLGVBQWU7SUFFZixhQUFhO0lBQ2IsT0FBTztJQUNQLGtCQUFrQjtJQUNsQixXQUFXO0lBQ1gsV0FBVztBQUNiO0FBRUE7O0NBRUMsR0FDTSxTQUFTQyxjQUFjQyxZQUEwQjtJQUN0RCxNQUFNQyxTQUFTRCxhQUFhQyxNQUFNLENBQUNDLFdBQVc7SUFFOUMsMkJBQTJCO0lBQzNCLElBQUlDLGVBQWVMLGlCQUFpQixDQUFDRyxPQUFPO0lBRTVDLDZEQUE2RDtJQUM3RCxJQUFJLENBQUNFLGNBQWM7UUFDakIsS0FBSyxNQUFNLENBQUNDLGVBQWVDLFNBQVMsSUFBSUMsT0FBT0MsT0FBTyxDQUFDVCxtQkFBb0I7WUFDekUsSUFBSUcsT0FBT08sUUFBUSxDQUFDSixnQkFBZ0I7Z0JBQ2xDRCxlQUFlRTtnQkFDZjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLDBEQUEwRDtJQUMxRCxJQUFJLENBQUNGLGNBQWM7UUFDakIsSUFBSUYsT0FBT08sUUFBUSxDQUFDLFNBQVM7WUFDM0JMLGVBQWU7UUFDakIsT0FBTyxJQUFJRixPQUFPTyxRQUFRLENBQUMsU0FBUztZQUNsQ0wsZUFBZTtRQUNqQjtJQUNGO0lBRUEsMENBQTBDO0lBQzFDLElBQUksQ0FBQ0EsY0FBYztRQUNqQkEsZUFBZTtJQUNqQjtJQUVBLE9BQU87UUFDTCxHQUFHSCxZQUFZO1FBQ2ZLLFVBQVVYLGNBQWMsQ0FBQ1MsYUFBYTtJQUN4QztBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTTSxlQUFlQyxhQUE2QjtJQUMxRCxPQUFPQSxjQUFjQyxHQUFHLENBQUNaO0FBQzNCO0FBRUE7O0NBRUMsR0FDTSxTQUFTYSxvQkFBb0JDLGVBQWlDO0lBQ25FLE9BQU9BLGdCQUFnQkMsTUFBTSxDQUFDLENBQUNDLFFBQVFDO1FBQ3JDLE1BQU1iLGVBQWVhLElBQUlYLFFBQVEsQ0FBQ1YsSUFBSTtRQUN0QyxJQUFJLENBQUNvQixNQUFNLENBQUNaLGFBQWEsRUFBRTtZQUN6QlksTUFBTSxDQUFDWixhQUFhLEdBQUcsRUFBRTtRQUMzQjtRQUNBWSxNQUFNLENBQUNaLGFBQWEsQ0FBQ2MsSUFBSSxDQUFDRDtRQUMxQixPQUFPRDtJQUNULEdBQUcsQ0FBQztBQUNOO0FBRUE7O0NBRUMsR0FDTSxTQUFTRyxpQkFBaUJMLGVBQWlDO0lBQ2hFLE9BQU9BLGdCQUFnQkMsTUFBTSxDQUFDLENBQUNLLE9BQU9IO1FBQ3BDLE1BQU1iLGVBQWVhLElBQUlYLFFBQVEsQ0FBQ1YsSUFBSTtRQUN0Q3dCLEtBQUssQ0FBQ2hCLGFBQWEsR0FBRyxDQUFDZ0IsS0FBSyxDQUFDaEIsYUFBYSxJQUFJLEtBQUs7UUFDbkQsT0FBT2dCO0lBQ1QsR0FBRyxDQUFDO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly91cmwtY2F0ZWdvcml6ZXIvLi9zcmMvdXRpbHMvdXJsQ2F0ZWdvcml6ZXIudHM/ZWU5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFeHRyYWN0ZWRVcmwgfSBmcm9tICcuL3VybEV4dHJhY3Rvcic7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXJsQ2F0ZWdvcnkge1xuICBuYW1lOiBzdHJpbmc7XG4gIGNvbG9yOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2F0ZWdvcml6ZWRVcmwgZXh0ZW5kcyBFeHRyYWN0ZWRVcmwge1xuICBjYXRlZ29yeTogVXJsQ2F0ZWdvcnk7XG59XG5cbi8vIERlZmluZSBVUkwgY2F0ZWdvcmllc1xuZXhwb3J0IGNvbnN0IFVSTF9DQVRFR09SSUVTOiBSZWNvcmQ8c3RyaW5nLCBVcmxDYXRlZ29yeT4gPSB7XG4gICdTb2NpYWwgTWVkaWEnOiB7XG4gICAgbmFtZTogJ1NvY2lhbCBNZWRpYScsXG4gICAgY29sb3I6ICcjM0I4MkY2JyxcbiAgICBkZXNjcmlwdGlvbjogJ1NvY2lhbCBuZXR3b3JraW5nIGFuZCBtZWRpYSBzaGFyaW5nIHBsYXRmb3JtcydcbiAgfSxcbiAgJ05ld3MgJiBNZWRpYSc6IHtcbiAgICBuYW1lOiAnTmV3cyAmIE1lZGlhJyxcbiAgICBjb2xvcjogJyNFRjQ0NDQnLFxuICAgIGRlc2NyaXB0aW9uOiAnTmV3cyB3ZWJzaXRlcyBhbmQgbWVkaWEgb3V0bGV0cydcbiAgfSxcbiAgJ0UtY29tbWVyY2UnOiB7XG4gICAgbmFtZTogJ0UtY29tbWVyY2UnLFxuICAgIGNvbG9yOiAnIzEwQjk4MScsXG4gICAgZGVzY3JpcHRpb246ICdPbmxpbmUgc2hvcHBpbmcgYW5kIG1hcmtldHBsYWNlIHdlYnNpdGVzJ1xuICB9LFxuICAnVGVjaG5vbG9neSc6IHtcbiAgICBuYW1lOiAnVGVjaG5vbG9neScsXG4gICAgY29sb3I6ICcjOEI1Q0Y2JyxcbiAgICBkZXNjcmlwdGlvbjogJ1RlY2hub2xvZ3kgY29tcGFuaWVzIGFuZCB0ZWNoIG5ld3MnXG4gIH0sXG4gICdFbnRlcnRhaW5tZW50Jzoge1xuICAgIG5hbWU6ICdFbnRlcnRhaW5tZW50JyxcbiAgICBjb2xvcjogJyNGNTlFMEInLFxuICAgIGRlc2NyaXB0aW9uOiAnRW50ZXJ0YWlubWVudCwgZ2FtaW5nLCBhbmQgc3RyZWFtaW5nIHBsYXRmb3JtcydcbiAgfSxcbiAgJ0VkdWNhdGlvbic6IHtcbiAgICBuYW1lOiAnRWR1Y2F0aW9uJyxcbiAgICBjb2xvcjogJyMwNkI2RDQnLFxuICAgIGRlc2NyaXB0aW9uOiAnRWR1Y2F0aW9uYWwgaW5zdGl0dXRpb25zIGFuZCBsZWFybmluZyBwbGF0Zm9ybXMnXG4gIH0sXG4gICdCdXNpbmVzcyc6IHtcbiAgICBuYW1lOiAnQnVzaW5lc3MnLFxuICAgIGNvbG9yOiAnIzg0Q0MxNicsXG4gICAgZGVzY3JpcHRpb246ICdCdXNpbmVzcyBhbmQgcHJvZmVzc2lvbmFsIHNlcnZpY2VzJ1xuICB9LFxuICAnR292ZXJubWVudCc6IHtcbiAgICBuYW1lOiAnR292ZXJubWVudCcsXG4gICAgY29sb3I6ICcjNkI3MjgwJyxcbiAgICBkZXNjcmlwdGlvbjogJ0dvdmVybm1lbnQgYW5kIG9mZmljaWFsIHdlYnNpdGVzJ1xuICB9LFxuICAnT3RoZXInOiB7XG4gICAgbmFtZTogJ090aGVyJyxcbiAgICBjb2xvcjogJyM5Q0EzQUYnLFxuICAgIGRlc2NyaXB0aW9uOiAnVW5jYXRlZ29yaXplZCB3ZWJzaXRlcydcbiAgfVxufTtcblxuLy8gRG9tYWluIHRvIGNhdGVnb3J5IG1hcHBpbmdcbmNvbnN0IERPTUFJTl9DQVRFR09SSUVTOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAvLyBTb2NpYWwgTWVkaWFcbiAgJ2ZhY2Vib29rLmNvbSc6ICdTb2NpYWwgTWVkaWEnLFxuICAnaW5zdGFncmFtLmNvbSc6ICdTb2NpYWwgTWVkaWEnLFxuICAndGlrdG9rLmNvbSc6ICdTb2NpYWwgTWVkaWEnLFxuICAnbGVtb244LWFwcC5jb20nOiAnU29jaWFsIE1lZGlhJyxcbiAgJ3R3aXR0ZXIuY29tJzogJ1NvY2lhbCBNZWRpYScsXG4gICd4LmNvbSc6ICdTb2NpYWwgTWVkaWEnLFxuICAnbGlua2VkaW4uY29tJzogJ1NvY2lhbCBNZWRpYScsXG4gICdzbmFwY2hhdC5jb20nOiAnU29jaWFsIE1lZGlhJyxcbiAgJ3BpbnRlcmVzdC5jb20nOiAnU29jaWFsIE1lZGlhJyxcbiAgJ3JlZGRpdC5jb20nOiAnU29jaWFsIE1lZGlhJyxcbiAgJ2Rpc2NvcmQuY29tJzogJ1NvY2lhbCBNZWRpYScsXG4gICd0ZWxlZ3JhbS5vcmcnOiAnU29jaWFsIE1lZGlhJyxcbiAgJ3doYXRzYXBwLmNvbSc6ICdTb2NpYWwgTWVkaWEnLFxuICAneW91dHViZS5jb20nOiAnU29jaWFsIE1lZGlhJyxcbiAgJ3ZpbWVvLmNvbSc6ICdTb2NpYWwgTWVkaWEnLFxuICBcbiAgLy8gTmV3cyAmIE1lZGlhXG4gICdjbm4uY29tJzogJ05ld3MgJiBNZWRpYScsXG4gICdiYmMuY29tJzogJ05ld3MgJiBNZWRpYScsXG4gICdueXRpbWVzLmNvbSc6ICdOZXdzICYgTWVkaWEnLFxuICAnd2FzaGluZ3RvbnBvc3QuY29tJzogJ05ld3MgJiBNZWRpYScsXG4gICdyZXV0ZXJzLmNvbSc6ICdOZXdzICYgTWVkaWEnLFxuICAnYXAub3JnJzogJ05ld3MgJiBNZWRpYScsXG4gICducHIub3JnJzogJ05ld3MgJiBNZWRpYScsXG4gICd0aGVndWFyZGlhbi5jb20nOiAnTmV3cyAmIE1lZGlhJyxcbiAgJ3dzai5jb20nOiAnTmV3cyAmIE1lZGlhJyxcbiAgJ2Jsb29tYmVyZy5jb20nOiAnTmV3cyAmIE1lZGlhJyxcbiAgXG4gIC8vIEUtY29tbWVyY2VcbiAgJ2FtYXpvbi5jb20nOiAnRS1jb21tZXJjZScsXG4gICdlYmF5LmNvbSc6ICdFLWNvbW1lcmNlJyxcbiAgJ2V0c3kuY29tJzogJ0UtY29tbWVyY2UnLFxuICAnc2hvcGlmeS5jb20nOiAnRS1jb21tZXJjZScsXG4gICd3YWxtYXJ0LmNvbSc6ICdFLWNvbW1lcmNlJyxcbiAgJ3RhcmdldC5jb20nOiAnRS1jb21tZXJjZScsXG4gICdhbGliYWJhLmNvbSc6ICdFLWNvbW1lcmNlJyxcbiAgJ2FsaWV4cHJlc3MuY29tJzogJ0UtY29tbWVyY2UnLFxuICBcbiAgLy8gVGVjaG5vbG9neVxuICAnZ29vZ2xlLmNvbSc6ICdUZWNobm9sb2d5JyxcbiAgJ21pY3Jvc29mdC5jb20nOiAnVGVjaG5vbG9neScsXG4gICdhcHBsZS5jb20nOiAnVGVjaG5vbG9neScsXG4gICdnaXRodWIuY29tJzogJ1RlY2hub2xvZ3knLFxuICAnc3RhY2tvdmVyZmxvdy5jb20nOiAnVGVjaG5vbG9neScsXG4gICd0ZWNoY3J1bmNoLmNvbSc6ICdUZWNobm9sb2d5JyxcbiAgJ3dpcmVkLmNvbSc6ICdUZWNobm9sb2d5JyxcbiAgJ3ZlcmdlLmNvbSc6ICdUZWNobm9sb2d5JyxcbiAgXG4gIC8vIEVudGVydGFpbm1lbnRcbiAgJ25ldGZsaXguY29tJzogJ0VudGVydGFpbm1lbnQnLFxuICAnaHVsdS5jb20nOiAnRW50ZXJ0YWlubWVudCcsXG4gICdkaXNuZXkuY29tJzogJ0VudGVydGFpbm1lbnQnLFxuICAnc3BvdGlmeS5jb20nOiAnRW50ZXJ0YWlubWVudCcsXG4gICd0d2l0Y2gudHYnOiAnRW50ZXJ0YWlubWVudCcsXG4gICdzdGVhbS5jb20nOiAnRW50ZXJ0YWlubWVudCcsXG4gICdpbWRiLmNvbSc6ICdFbnRlcnRhaW5tZW50JyxcbiAgXG4gIC8vIEVkdWNhdGlvblxuICAnY291cnNlcmEub3JnJzogJ0VkdWNhdGlvbicsXG4gICdlZHgub3JnJzogJ0VkdWNhdGlvbicsXG4gICdraGFuYWNhZGVteS5vcmcnOiAnRWR1Y2F0aW9uJyxcbiAgJ3VkZW15LmNvbSc6ICdFZHVjYXRpb24nLFxuICAnbWl0LmVkdSc6ICdFZHVjYXRpb24nLFxuICAnaGFydmFyZC5lZHUnOiAnRWR1Y2F0aW9uJyxcbiAgJ3N0YW5mb3JkLmVkdSc6ICdFZHVjYXRpb24nLFxuICBcbiAgLy8gQnVzaW5lc3NcbiAgJ3NhbGVzZm9yY2UuY29tJzogJ0J1c2luZXNzJyxcbiAgJ2h1YnNwb3QuY29tJzogJ0J1c2luZXNzJyxcbiAgJ3NsYWNrLmNvbSc6ICdCdXNpbmVzcycsXG4gICd6b29tLnVzJzogJ0J1c2luZXNzJyxcbiAgJ2Ryb3Bib3guY29tJzogJ0J1c2luZXNzJyxcbiAgXG4gIC8vIEdvdmVybm1lbnRcbiAgJ2dvdic6ICdHb3Zlcm5tZW50JyxcbiAgJ3doaXRlaG91c2UuZ292JzogJ0dvdmVybm1lbnQnLFxuICAnaXJzLmdvdic6ICdHb3Zlcm5tZW50JyxcbiAgJ2NkYy5nb3YnOiAnR292ZXJubWVudCdcbn07XG5cbi8qKlxuICogQ2F0ZWdvcml6ZXMgYSBVUkwgYmFzZWQgb24gaXRzIGRvbWFpblxuICovXG5leHBvcnQgZnVuY3Rpb24gY2F0ZWdvcml6ZVVybChleHRyYWN0ZWRVcmw6IEV4dHJhY3RlZFVybCk6IENhdGVnb3JpemVkVXJsIHtcbiAgY29uc3QgZG9tYWluID0gZXh0cmFjdGVkVXJsLmRvbWFpbi50b0xvd2VyQ2FzZSgpO1xuICBcbiAgLy8gQ2hlY2sgZXhhY3QgZG9tYWluIG1hdGNoXG4gIGxldCBjYXRlZ29yeU5hbWUgPSBET01BSU5fQ0FURUdPUklFU1tkb21haW5dO1xuICBcbiAgLy8gQ2hlY2sgZm9yIHN1YmRvbWFpbiBtYXRjaGVzIChlLmcuLCBzdWJkb21haW4uZmFjZWJvb2suY29tKVxuICBpZiAoIWNhdGVnb3J5TmFtZSkge1xuICAgIGZvciAoY29uc3QgW2RvbWFpblBhdHRlcm4sIGNhdGVnb3J5XSBvZiBPYmplY3QuZW50cmllcyhET01BSU5fQ0FURUdPUklFUykpIHtcbiAgICAgIGlmIChkb21haW4uZW5kc1dpdGgoZG9tYWluUGF0dGVybikpIHtcbiAgICAgICAgY2F0ZWdvcnlOYW1lID0gY2F0ZWdvcnk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBcbiAgLy8gQ2hlY2sgZm9yIFRMRC1iYXNlZCBjYXRlZ29yaXphdGlvbiAoZS5nLiwgLmdvdiBkb21haW5zKVxuICBpZiAoIWNhdGVnb3J5TmFtZSkge1xuICAgIGlmIChkb21haW4uZW5kc1dpdGgoJy5nb3YnKSkge1xuICAgICAgY2F0ZWdvcnlOYW1lID0gJ0dvdmVybm1lbnQnO1xuICAgIH0gZWxzZSBpZiAoZG9tYWluLmVuZHNXaXRoKCcuZWR1JykpIHtcbiAgICAgIGNhdGVnb3J5TmFtZSA9ICdFZHVjYXRpb24nO1xuICAgIH1cbiAgfVxuICBcbiAgLy8gRGVmYXVsdCB0byAnT3RoZXInIGlmIG5vIGNhdGVnb3J5IGZvdW5kXG4gIGlmICghY2F0ZWdvcnlOYW1lKSB7XG4gICAgY2F0ZWdvcnlOYW1lID0gJ090aGVyJztcbiAgfVxuICBcbiAgcmV0dXJuIHtcbiAgICAuLi5leHRyYWN0ZWRVcmwsXG4gICAgY2F0ZWdvcnk6IFVSTF9DQVRFR09SSUVTW2NhdGVnb3J5TmFtZV1cbiAgfTtcbn1cblxuLyoqXG4gKiBDYXRlZ29yaXplcyBtdWx0aXBsZSBVUkxzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjYXRlZ29yaXplVXJscyhleHRyYWN0ZWRVcmxzOiBFeHRyYWN0ZWRVcmxbXSk6IENhdGVnb3JpemVkVXJsW10ge1xuICByZXR1cm4gZXh0cmFjdGVkVXJscy5tYXAoY2F0ZWdvcml6ZVVybCk7XG59XG5cbi8qKlxuICogR3JvdXBzIGNhdGVnb3JpemVkIFVSTHMgYnkgY2F0ZWdvcnlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdyb3VwVXJsc0J5Q2F0ZWdvcnkoY2F0ZWdvcml6ZWRVcmxzOiBDYXRlZ29yaXplZFVybFtdKTogUmVjb3JkPHN0cmluZywgQ2F0ZWdvcml6ZWRVcmxbXT4ge1xuICByZXR1cm4gY2F0ZWdvcml6ZWRVcmxzLnJlZHVjZSgoZ3JvdXBzLCB1cmwpID0+IHtcbiAgICBjb25zdCBjYXRlZ29yeU5hbWUgPSB1cmwuY2F0ZWdvcnkubmFtZTtcbiAgICBpZiAoIWdyb3Vwc1tjYXRlZ29yeU5hbWVdKSB7XG4gICAgICBncm91cHNbY2F0ZWdvcnlOYW1lXSA9IFtdO1xuICAgIH1cbiAgICBncm91cHNbY2F0ZWdvcnlOYW1lXS5wdXNoKHVybCk7XG4gICAgcmV0dXJuIGdyb3VwcztcbiAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgQ2F0ZWdvcml6ZWRVcmxbXT4pO1xufVxuXG4vKipcbiAqIEdldHMgY2F0ZWdvcnkgc3RhdGlzdGljc1xuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q2F0ZWdvcnlTdGF0cyhjYXRlZ29yaXplZFVybHM6IENhdGVnb3JpemVkVXJsW10pOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+IHtcbiAgcmV0dXJuIGNhdGVnb3JpemVkVXJscy5yZWR1Y2UoKHN0YXRzLCB1cmwpID0+IHtcbiAgICBjb25zdCBjYXRlZ29yeU5hbWUgPSB1cmwuY2F0ZWdvcnkubmFtZTtcbiAgICBzdGF0c1tjYXRlZ29yeU5hbWVdID0gKHN0YXRzW2NhdGVnb3J5TmFtZV0gfHwgMCkgKyAxO1xuICAgIHJldHVybiBzdGF0cztcbiAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgbnVtYmVyPik7XG59XG4iXSwibmFtZXMiOlsiVVJMX0NBVEVHT1JJRVMiLCJuYW1lIiwiY29sb3IiLCJkZXNjcmlwdGlvbiIsIkRPTUFJTl9DQVRFR09SSUVTIiwiY2F0ZWdvcml6ZVVybCIsImV4dHJhY3RlZFVybCIsImRvbWFpbiIsInRvTG93ZXJDYXNlIiwiY2F0ZWdvcnlOYW1lIiwiZG9tYWluUGF0dGVybiIsImNhdGVnb3J5IiwiT2JqZWN0IiwiZW50cmllcyIsImVuZHNXaXRoIiwiY2F0ZWdvcml6ZVVybHMiLCJleHRyYWN0ZWRVcmxzIiwibWFwIiwiZ3JvdXBVcmxzQnlDYXRlZ29yeSIsImNhdGVnb3JpemVkVXJscyIsInJlZHVjZSIsImdyb3VwcyIsInVybCIsInB1c2giLCJnZXRDYXRlZ29yeVN0YXRzIiwic3RhdHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/urlCategorizer.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/urlExtractor.ts":
/*!***********************************!*\
  !*** ./src/utils/urlExtractor.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractDomain: () => (/* binding */ extractDomain),\n/* harmony export */   extractUrls: () => (/* binding */ extractUrls),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl)\n/* harmony export */ });\n/**\n * Extracts URLs from text content using comprehensive regex patterns\n * Handles various URL formats including http, https, www, and domain-only URLs\n */ function extractUrls(text) {\n    const urls = [];\n    // Comprehensive URL regex patterns\n    const urlPatterns = [\n        // Full URLs with protocol\n        /https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)/gi,\n        // URLs starting with www\n        /www\\.[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)/gi,\n        // Domain-only URLs (more conservative pattern)\n        /(?:^|\\s)([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}(?:\\s|$)/gi\n    ];\n    urlPatterns.forEach((pattern)=>{\n        let match;\n        while((match = pattern.exec(text)) !== null){\n            const originalText = match[0].trim();\n            let url = originalText;\n            // Normalize URL\n            if (!url.startsWith(\"http\") && !url.startsWith(\"www\")) {\n                // For domain-only matches, add protocol\n                url = `https://${url}`;\n            } else if (url.startsWith(\"www\")) {\n                url = `https://${url}`;\n            }\n            try {\n                const urlObj = new URL(url);\n                const domain = urlObj.hostname.replace(/^www\\./, \"\");\n                // Avoid duplicates\n                if (!urls.some((existingUrl)=>existingUrl.url === url)) {\n                    urls.push({\n                        url,\n                        originalText,\n                        domain,\n                        protocol: urlObj.protocol\n                    });\n                }\n            } catch (error) {\n                continue;\n            }\n        }\n    });\n    return urls;\n}\n/**\n * Validates if a string is a valid URL\n */ function isValidUrl(urlString) {\n    try {\n        new URL(urlString);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\n * Extracts domain from URL string\n */ function extractDomain(url) {\n    try {\n        const urlObj = new URL(url.startsWith(\"http\") ? url : `https://${url}`);\n        return urlObj.hostname.replace(/^www\\./, \"\");\n    } catch  {\n        return \"\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/urlExtractor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=C%3A%5CSpaces%5CDaisyLogsCursor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CSpaces%5CDaisyLogsCursor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();